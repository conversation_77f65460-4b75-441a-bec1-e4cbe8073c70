// Konfigurace <PERSON>
const tasks = [
    { id: 1, name: "Úkol 1", description: "<PERSON>rv<PERSON><PERSON> úkol", type: "HTML/CSS", folder: "ukol1" },
    { id: 2, name: "Úkol 2", description: "<PERSON><PERSON><PERSON> úkol", type: "CSS", folder: "ukol2" },
    { id: 3, name: "Úkol 3", description: "Třetí úkol", type: "Responsive", folder: "ukol3" },
    { id: 4, name: "Úkol 4", description: "Čtvrtý úkol", type: "JavaScript", folder: "ukol4" },
    { id: 5, name: "Úkol 5", description: "<PERSON><PERSON>tý úkol", type: "Backend", folder: "ukol5" },
    { id: 6, name: "Úkol 6", description: "Šestý úkol", type: "Data", folder: "ukol6" },
    { id: 7, name: "Úkol 7", description: "Sedmý úkol", type: "Interaktivní", folder: "ukol7" },
    { id: 8, name: "Úkol 8", description: "<PERSON><PERSON><PERSON>", type: "E-commerce", folder: "ukol8" },
    { id: 9, name: "Úkol 9", description: "Devát<PERSON> úkol", type: "Sociální", folder: "ukol9" },
    { id: 10, name: "Úkol 10", description: "Desátý úkol", type: "Finální", folder: "ukol10" }
];

// Stav úkolů (bude se načítat z localStorage nebo detekovat automaticky)
let taskStatus = {};

// Inicializace při načtení stránky
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    loadTaskStatus();
    checkProfileImage();
    updateTaskCards();
    updateStats();
    setupEventListeners();
}

// Načtení stavu úkolů z localStorage
function loadTaskStatus() {
    const saved = localStorage.getItem('taskStatus');
    if (saved) {
        taskStatus = JSON.parse(saved);
    } else {
        // Výchozí stav - všechny úkoly nedokončené
        tasks.forEach(task => {
            taskStatus[task.id] = false;
        });
    }
}

// Uložení stavu úkolů do localStorage
function saveTaskStatus() {
    localStorage.setItem('taskStatus', JSON.stringify(taskStatus));
}

// Kontrola existence profilové fotografie
function checkProfileImage() {
    const profileImg = document.getElementById('profile-img');
    const profilePlaceholder = document.getElementById('profile-placeholder');
    
    profileImg.onerror = function() {
        profileImg.style.display = 'none';
        profilePlaceholder.style.display = 'flex';
    };
    
    profileImg.onload = function() {
        profileImg.style.display = 'block';
        profilePlaceholder.style.display = 'none';
    };
}

// Aktualizace karet úkolů
function updateTaskCards() {
    tasks.forEach(task => {
        const statusElement = document.getElementById(`status-${task.id}`);
        const linkElement = document.getElementById(`link-${task.id}`);
        const taskDateElement = document.querySelector(`[data-task="${task.id}"] .task-date`);
        
        if (taskStatus[task.id]) {
            // Úkol je dokončený
            statusElement.className = 'task-status completed';
            statusElement.innerHTML = '<i class="fas fa-check"></i>';
            taskDateElement.textContent = 'Dokončeno';
            linkElement.classList.remove('btn-disabled');
        } else {
            // Kontrola existence souboru
            checkTaskExists(task.id, task.folder).then(exists => {
                if (exists) {
                    statusElement.className = 'task-status pending';
                    statusElement.innerHTML = '<i class="fas fa-exclamation"></i>';
                    taskDateElement.textContent = 'K dokončení';
                    linkElement.classList.remove('btn-disabled');
                } else {
                    statusElement.className = 'task-status';
                    statusElement.innerHTML = '<i class="fas fa-clock"></i>';
                    taskDateElement.textContent = 'Nedokončeno';
                    linkElement.classList.add('btn-disabled');
                    linkElement.onclick = function(e) {
                        e.preventDefault();
                        showTaskNotFound(task.id);
                    };
                }
            });
        }
    });
}

// Kontrola existence úkolu
async function checkTaskExists(taskId, folder) {
    try {
        const response = await fetch(`${folder}/index.html`, { method: 'HEAD' });
        return response.ok;
    } catch (error) {
        return false;
    }
}

// Zobrazení zprávy o nenalezeném úkolu
function showTaskNotFound(taskId) {
    const message = document.createElement('div');
    message.className = 'notification error';
    message.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i>
        Úkol ${taskId} ještě nebyl vytvořen
    `;
    
    document.body.appendChild(message);
    
    setTimeout(() => {
        message.remove();
    }, 3000);
}

// Aktualizace statistik
function updateStats() {
    const completedCount = Object.values(taskStatus).filter(status => status).length;
    const totalCount = tasks.length;
    const progressPercent = Math.round((completedCount / totalCount) * 100);
    
    document.getElementById('completed-count').textContent = completedCount;
    document.getElementById('total-count').textContent = totalCount;
    document.getElementById('progress-percent').textContent = `${progressPercent}%`;
    
    // Aktualizace progress baru
    const progressFill = document.getElementById('progress-fill');
    progressFill.style.width = `${progressPercent}%`;
}

// Nastavení event listenerů
function setupEventListeners() {
    // Kliknutí na kartu úkolu pro označení jako dokončený
    document.querySelectorAll('.task-card').forEach(card => {
        const taskId = parseInt(card.dataset.task);
        
        // Double-click pro označení jako dokončený/nedokončený
        card.addEventListener('dblclick', function() {
            toggleTaskStatus(taskId);
        });
        
        // Tooltip pro double-click
        card.title = 'Double-click pro označení jako dokončený/nedokončený';
    });
    
    // Klávesové zkratky
    document.addEventListener('keydown', function(e) {
        // Ctrl + R pro reset všech úkolů
        if (e.ctrlKey && e.key === 'r') {
            e.preventDefault();
            resetAllTasks();
        }
        
        // Ctrl + A pro označení všech jako dokončené
        if (e.ctrlKey && e.key === 'a') {
            e.preventDefault();
            markAllCompleted();
        }
    });
}

// Přepnutí stavu úkolu
function toggleTaskStatus(taskId) {
    taskStatus[taskId] = !taskStatus[taskId];
    saveTaskStatus();
    updateTaskCards();
    updateStats();
    
    // Zobrazení notifikace
    const task = tasks.find(t => t.id === taskId);
    const status = taskStatus[taskId] ? 'dokončen' : 'označen jako nedokončený';
    showNotification(`${task.name} byl ${status}`, taskStatus[taskId] ? 'success' : 'info');
}

// Reset všech úkolů
function resetAllTasks() {
    if (confirm('Opravdu chcete resetovat stav všech úkolů?')) {
        tasks.forEach(task => {
            taskStatus[task.id] = false;
        });
        saveTaskStatus();
        updateTaskCards();
        updateStats();
        showNotification('Všechny úkoly byly resetovány', 'info');
    }
}

// Označení všech jako dokončené
function markAllCompleted() {
    if (confirm('Označit všechny úkoly jako dokončené?')) {
        tasks.forEach(task => {
            taskStatus[task.id] = true;
        });
        saveTaskStatus();
        updateTaskCards();
        updateStats();
        showNotification('Všechny úkoly označeny jako dokončené', 'success');
    }
}

// Zobrazení notifikace
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    
    const icon = type === 'success' ? 'check' : type === 'error' ? 'times' : 'info';
    notification.innerHTML = `
        <i class="fas fa-${icon}"></i>
        ${message}
    `;
    
    // Styly pro notifikaci
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#48bb78' : type === 'error' ? '#f56565' : '#667eea'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        z-index: 1000;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 500;
        animation: slideInRight 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// CSS animace pro notifikace
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .btn-disabled {
        opacity: 0.5;
        cursor: not-allowed;
        pointer-events: none;
    }
`;
document.head.appendChild(style);

// Automatická kontrola úkolů každých 30 sekund
setInterval(() => {
    updateTaskCards();
}, 30000);
