# Rozcestník školních úkolů - Matěj

Moderní responzivní rozcestník pro prezentaci školních úkolů.

## Funkce

- **Přehledný dashboard** s náhledy všech úkolů
- **<PERSON><PERSON><PERSON> dete<PERSON>ce** dokončených úkolů
- **Responzivní design** pro všechna zařízení
- **Interaktivní prvky** s hover efekty
- **Progress tracking** s vizuálním ukazatelem pokroku
- **Profilová sekce** s fotografií autora

## Struktura

```
prx3/
├── index.html          # Hlavní rozcestník
├── styles.css          # CSS styly
├── script.js           # JavaScript funkce
├── profile.jpg         # Profilová fotografie (přidejte vlastní)
├── ukol1/             # Složka pro úkol 1
│   └── index.html     # Obsah úkolu 1
├── ukol2/             # Složka pro úkol 2
├── ...                # <PERSON><PERSON><PERSON> úkoly
└── ukol10/            # Složka pro úkol 10
```

## Použití

1. **Přidání profilové fotografie**: Nahrajte svou fotografii jako `profile.jpg` do hlavní složky
2. **Vytváření úkolů**: Do každé složky `ukolX/` přidejte `index.html` s obsahem úkolu
3. **Označování dokončených úkolů**: Double-click na kartu úkolu pro označení jako dokončený

## Funkce rozcestníku

### Automatická detekce úkolů
- Zelená ikona ✓ = úkol dokončen
- Oranžová ikona ! = úkol existuje, ale není označen jako dokončený  
- Žlutá ikona ⏰ = úkol ještě neexistuje

### Klávesové zkratky
- `Ctrl + A` = označit všechny úkoly jako dokončené
- `Ctrl + R` = resetovat všechny úkoly

### Responzivní design
- Desktop: grid layout s 3-4 sloupci
- Tablet: 2 sloupce
- Mobil: 1 sloupec

## Přizpůsobení

### Změna informací o autorovi
Upravte v `index.html`:
```html
<h1>Vaše jméno</h1>
<p class="subtitle">Váš popis</p>
```

### Přidání vlastních úkolů
1. Vytvořte složku `ukolX/`
2. Přidejte `index.html` s obsahem
3. Rozcestník automaticky detekuje nový úkol

### Změna vzhledu
Upravte CSS proměnné v `styles.css` pro změnu barev a stylů.

## Technologie

- **HTML5** - sémantická struktura
- **CSS3** - moderní styly s Grid a Flexbox
- **JavaScript ES6+** - interaktivní funkce
- **Font Awesome** - ikony
- **Google Fonts** - typografie (Inter)

## Kompatibilita

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
