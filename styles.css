/* Reset a základní styly */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

/* Header styly */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.profile-section {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.profile-image {
    position: relative;
    width: 100px;
    height: 100px;
}

.profile-image img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #667eea;
    display: block;
}

.profile-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: none;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    border: 4px solid #667eea;
}

.profile-info h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.subtitle {
    font-size: 1.1rem;
    color: #718096;
    margin-bottom: 1rem;
}

.stats {
    display: flex;
    gap: 2rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    color: #667eea;
}

.stat-label {
    font-size: 0.9rem;
    color: #718096;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

/* Grid úkolů */
.tasks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.task-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.task-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.task-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    background: #ffd700;
    color: #333;
    transition: all 0.3s ease;
}

.task-status.completed {
    background: #48bb78;
    color: white;
}

.task-status.pending {
    background: #ed8936;
    color: white;
}

.task-preview {
    margin-bottom: 1rem;
}

.task-thumbnail {
    width: 100%;
    height: 120px;
    background: linear-gradient(135deg, #f7fafc, #edf2f7);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: #667eea;
    border: 2px dashed #e2e8f0;
    transition: all 0.3s ease;
}

.task-card:hover .task-thumbnail {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;
}

.task-info h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.task-description {
    color: #718096;
    margin-bottom: 1rem;
    font-size: 0.95rem;
}

.task-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.task-type {
    background: #667eea;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.task-date {
    font-size: 0.85rem;
    color: #718096;
}

.task-actions {
    margin-top: 1rem;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Footer */
.footer {
    text-align: center;
    padding: 2rem;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

/* Responzivní design */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }

    .profile-section {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .profile-info h1 {
        font-size: 2rem;
    }

    .stats {
        justify-content: center;
    }

    .tasks-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .task-card {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .profile-image {
        width: 80px;
        height: 80px;
    }

    .profile-info h1 {
        font-size: 1.8rem;
    }

    .stats {
        gap: 1rem;
    }

    .task-meta {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }
}

/* Animace pro načítání */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.task-card {
    animation: fadeInUp 0.6s ease forwards;
}

.task-card:nth-child(1) { animation-delay: 0.1s; }
.task-card:nth-child(2) { animation-delay: 0.2s; }
.task-card:nth-child(3) { animation-delay: 0.3s; }
.task-card:nth-child(4) { animation-delay: 0.4s; }
.task-card:nth-child(5) { animation-delay: 0.5s; }
.task-card:nth-child(6) { animation-delay: 0.6s; }
.task-card:nth-child(7) { animation-delay: 0.7s; }
.task-card:nth-child(8) { animation-delay: 0.8s; }
.task-card:nth-child(9) { animation-delay: 0.9s; }
.task-card:nth-child(10) { animation-delay: 1.0s; }
